﻿namespace Data.Enums
{
    public enum ItemEffectType
    {
        RaisesMaxHp,
        RaisesMaxMp,
        GainMp,
        IncreasesMpRegenWhenComboAttackByPercents,
        IncreasesCritChanse,
        IncreasesMovementSpeedByPercents,
        IncreasesAttackSpeedByPercents,
        IncreasesDamageWhenAttackMonstersFromBehindByPercents,
        IncreasesDamageWhenAttackBossMonstersByPercents,
        AbsorbsUpWithStandFastSkill,
        MoreAggroByPercents,
        AbsorbsUpWithAxeBlockSkill,
        RecoverHp,
        ReceiveMoreHealingByPercents,
        LessAggroByPercents,
        ExtraDamageToTheTargetWithHighestAggroToYouByPercents,
        TakeLessDamageFromTheMonstersWithHighestAggroToYouByPercents,
        IncreasesResistToKnockdownWhileUsingSkillsByPercents,
        TakeLessDamageFromEnragedMonstersByPercents,
        MoreDamageWhenAttackNormalMonstersByPercents,
        MoreDamageWhenAttackSmallMonstersByPercents,
        IncreaseHealingByPercents,
        MoreDamageWhenAttackEnragedMonstersByPercents,
        MoreDamageWhenAttackProneMonstersByPercents,
        MpInstantlyReturnsWhenUseSkillByPercents,
        ChanseToRegenMpWhenCombatStartByPercents,
        CurseOfBeastImpact,
        CurseOfBeastIntoxicate,
        CurseOfBeastBinding,
        IncreasesDamageInPvPByPercents,
        IncreasesDamageInPvPFromBehindByPercents,
        EvilGodsCurseWeaken,
        EvilGodsCurseDecomposition,
        DemonicCurseFreezingPrison,
        DemonicCurseUnextinguishedFlame,
        DecreaseDamageFromSmallMonstersByPercents,
        DecreaseDamageFromBossMonstersByPercents,
        LessDamageWhileKnockdownByPercents,
        DecreaseDamageFromPlayersByPercents,
        DecreaseBehindDamageFromPlayersByPercents,
        LessDamageWhileKnockdownFromPlayersByPercents,
        InstantlyHealHpWhenHitTargetByPercents,
        ImprovesResistanceRateToCriticalHitsByPercents,
        TakeLessDamageFromFrontalAttackByPercents,
        IncreasesHpRestorationByPercents,
        ReflectDamageToTheAttackerByPercents,
        ChanseRegenerateHpWhenCombatStartByPercents,
        DecreaseDamageFromNormalMonstersByPercents,
        DecreasePoisonDamageByPercents,
        IncreaseResistanceToBridleOfDespairSkillByPercents,
        IncreaseRateOfOngoingDamageEffectByPercents,
        IncreaseRateOfWeakeningDamageEffectByPercents,
        IncreaseRateOfImmobilizeDamageEffectByPercents,
        IncreaseCriticalHitDamage,
        IncreaseRateToOngoingDamageEffectByPercents,
        IncreaseRateToWeakeningDamageEffectByPercents,
        IncreaseRateToImmobilizeDamageEffectByPercents,
        DecreaseDuracionOfStunEffectByPercents,
        TakeLessDamageFromCriticalHit,
        RaisesHpByPercents,
        IncreaseGatheringSkill,

        //itemuse effects
        BandageEffect,
        ClothExtractionLearn,
        MetalExtractionLearn,
        AlchemyExtractionLearn,
        LeatherExtractionLearn,
    }
}