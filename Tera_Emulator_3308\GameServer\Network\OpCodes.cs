using System;
using System.Collections.Generic;
using System.Linq;
using Network.Client;
using Network.Server;

namespace Network
{
    public class OpCodes
    {
        public static Dictionary<short, Type> Recv = new Dictionary<short, Type>();
        public static Dictionary<Type, short> Send = new Dictionary<Type, short>();

        public static Dictionary<short, string> SendNames = new Dictionary<short, string>();

        public static int Version = 3308;

        public static void Init()
        {
            #region Client packets

            // Auth
            Recv.Add(unchecked((short) 0x4DBC), typeof (CpRequestVersion)); //all revs
            Recv.Add(unchecked((short) 0xC644), typeof (CpRequestAuth)); //3308 RU

            Recv.Add(unchecked((short) 0xB04C), typeof (CpRequestSystemInfo)); //3308 RU
            //Recv.Add(unchecked((short) 0x83E0), typeof (CpRequestSystemInfo)); //3308 RU
            Recv.Add(unchecked((short) 0x6CE4), typeof (CpSystemInfo)); //3308 RU


            // Character
            Recv.Add(unchecked((short) 0xE5E4), typeof (CpRequestCharacterList)); //3308 RU
            Recv.Add(unchecked((short) 0x6755), typeof (CpCreateCharacter)); //3308 RU
            Recv.Add(unchecked((short) 0x8844), typeof (CpCheckName)); //3308 RU
            Recv.Add(unchecked((short) 0x7B3D), typeof (CpCheckNameForUse)); //3308 RU
            Recv.Add(unchecked((short) 0xBC40), typeof (CpDeleteCharacter)); //3308 RU




            // Enter World
            Recv.Add(unchecked((short) 0x8D6A), typeof (RpPlay)); //3308 RU
            Recv.Add(unchecked((short) 0xB61D), typeof (RpGetBindPoint)); //3308 RU
            Recv.Add(unchecked((short) 0xBCC4), typeof (RpEnterWorld)); //3308 RU

            Recv.Add(unchecked((short) 0x9AF7), typeof (RpEnterWorld)); //3308 RU
            // Chat
            Recv.Add(unchecked((short) 0xFBE6), typeof (RpChatMessage)); //3308 RU
            Recv.Add(unchecked((short) 0xE932), typeof (RpChatInfo)); //3308 RU
            Recv.Add(unchecked((short) 0xA8FA), typeof (RpChatPrivate)); //3308 RU
            Recv.Add(unchecked((short) 0xAF9D), typeof (RpChatBlock)); //3308 RU

            // Inactive
            Recv.Add(unchecked((short) 0xFA08), typeof (RpMove)); //3308 RU
            Recv.Add(unchecked((short) 0x837D), typeof (RpInactive)); //3308 RU

            // Inventory
            Recv.Add(unchecked((short) 0xBD08), typeof (RpGetInventory)); //3308 RU
            Recv.Add(unchecked((short) 0xA02D), typeof (RpInventoryRemoveItem)); //3308 RU
            Recv.Add(unchecked((short) 0xFEAD), typeof (RpInventoryReplaceItem)); //3308 RU
            Recv.Add(unchecked((short) 0x63D8), typeof (RpInventoryDressItem)); //3308 RU
            Recv.Add(unchecked((short) 0xB77E), typeof (RpInventoryUndressItem)); //3308 RU
            Recv.Add(unchecked((short) 0xC4BA), typeof (RpStorageSort)); //3308 RU

            // Dialog
            Recv.Add(unchecked((short) 0xBED7), typeof (RpDialogSelect)); //3308 RU
            Recv.Add(unchecked((short) 0xA7B1), typeof (RpGetItemInfo)); //3308 RU
            Recv.Add(unchecked((short) 0xB14B), typeof (RpGetSimpleItemInfo)); //3308 RU
            Recv.Add(unchecked((short) 0xADF4), typeof (RpRelog)); //3308 RU
            Recv.Add(unchecked((short) 0x99EB), typeof (RpAbortRelog)); //3308 RU
            Recv.Add(unchecked((short) 0xF19F), typeof (RpExit)); //3308 RU
            Recv.Add(unchecked((short) 0x878D), typeof (RpDialogShow)); //3308 RU
            Recv.Add(unchecked((short) 0x4EE4), typeof (RpItemPickUp)); //3308 RU

            // FriendList
            Recv.Add(unchecked((short) 0xC5C6), typeof (RpGetFriendList)); //3308 RU
            Recv.Add(unchecked((short) 0xE020), typeof (RpMoveToBind)); //3308 RU
            //Recv.Add(unchecked((short) 0xE806), typeof (RpGetCharacterEquipment)); //1513
            Recv.Add(unchecked((short) 0xC233), typeof (RpCharacterSettings1)); //3308 RU
            Recv.Add(unchecked((short) 0xA7A5), typeof (RpQuestTopSwitch)); //3308 RU
            Recv.Add(unchecked((short) 0xFC7C), typeof (RpQuestRefuse)); //3308 RU
            Recv.Add(unchecked((short) 0xAF84), typeof (RpTeleportCharacter)); //3308 RU

            // Trade And Sell
            Recv.Add(unchecked((short) 0xED71), typeof (RpAddToTrade)); // 3308 RU
            Recv.Add(unchecked((short) 0x7DD5), typeof (RpAddToExtract)); // 3308 RU
            Recv.Add(unchecked((short) 0xB73C), typeof (RpSellItem)); //3308 RU
            Recv.Add(unchecked((short) 0xF204), typeof (RpRemoveBuyTrade)); //3308 RU
            Recv.Add(unchecked((short) 0xC327), typeof (RpRemoveSellTrade)); //3308 RU
            Recv.Add(unchecked((short) 0x7219), typeof (RpCompleteTraid)); //3308 RU
            Recv.Add(unchecked((short) 0x5879), typeof (RpOwnerCancelRequest)); //3308 RU
            Recv.Add(unchecked((short) 0x5B72), typeof (RpRequestAccept)); // 3308 RU
            Recv.Add(unchecked((short) 0xB32B), typeof (RpRequestCancel)); // 3308 RU
            Recv.Add(unchecked((short) 0xF9A9), typeof (RpSystemRequest)); //3308 RU
            Recv.Add(unchecked((short) 0x6621), typeof (RpSkillBuy)); //3308 RU
            //Recv.Add(unchecked((short) 0x6A09), typeof (RpInviteUnk)); //1606
            Recv.Add(unchecked((short) 0xB2AA), typeof (RpDialogCancelRelog)); //3308 RU
            Recv.Add(unchecked((short) 0x819B), typeof (RpCharacterEmotion)); //3308 RU
            Recv.Add(unchecked((short) 0x8875), typeof (RpGatherStart)); //3308 RU
            Recv.Add(unchecked((short) 0xB351), typeof (RpFriendAdd)); //3308 RU
            Recv.Add(unchecked((short) 0xD500), typeof (RpFriendRemove)); //3308 RU
            Recv.Add(unchecked((short) 0xFC9B), typeof (RpInteruptAction)); //3308 RU
            Recv.Add(unchecked((short) 0xD2E0), typeof (RpRessurect)); //3308 RU
            Recv.Add(unchecked((short) 0x86E2), typeof (RpUnstuck)); //3308 RU

            //Inspect
            Recv.Add(unchecked((short) 0xC036), typeof (RpCharacterInspect)); //3308 RU
            Recv.Add(unchecked((short) 0x7A8D), typeof (RpGetInspectUid)); //3308 RU
            Recv.Add(unchecked((short) 0x91D7), typeof (RpInspectUnk)); //3308 RU

            //Climb
            Recv.Add(unchecked((short) 0xE364), typeof (RpClimb)); //3308 RU
            Recv.Add(unchecked((short) 0x89A4), typeof (RpClimbUp)); //3308 RU
            Recv.Add(unchecked((short) 0xF3CD), typeof (RpClimbEnd)); //3308 RU

            //Skills
            Recv.Add(unchecked((short) 0xBDEE), typeof (RpAttack)); //3308 RU
            Recv.Add(unchecked((short) 0x68CA), typeof (RpTargetAttack)); //3308 RU
            Recv.Add(unchecked((short) 0x8691), typeof (RpUseDelaySkill)); //3308 RU
            Recv.Add(unchecked((short) 0xC8B3), typeof (RpUseSkill)); //3308 RU //Move skills
            Recv.Add(unchecked((short) 0xF34A), typeof (RpUseCrosstargetSkill)); //3308 RU
            Recv.Add(unchecked((short) 0x7E21), typeof (RpUseItem)); // 3308 RU
            Recv.Add(unchecked((short) 0xB16A), typeof (RpMarkTarget)); //3308 RU
            Recv.Add(unchecked((short) 0x77BB), typeof (RpReleaseAttack)); //3308 RU

            //Party
            Recv.Add(unchecked((short) 0x86BD), typeof (RpPartyLeave)); //3308 RU
            Recv.Add(unchecked((short) 0x4F34), typeof (RpPartyDisband)); //3308 RU
            Recv.Add(unchecked((short) 0xF86C), typeof (RpPartyRemoveMember)); //3308 RU
            Recv.Add(unchecked((short) 0x82AE), typeof (RpPartyVote)); //3308 RU
            Recv.Add(unchecked((short) 0xBA24), typeof (RpPartyPromoteMember)); //3308 RU

            //Guilds
            Recv.Add(unchecked((short) 0x8781), typeof (RpGetServerGuilds)); //3308 RU
            Recv.Add(unchecked((short) 0xDB99), typeof (RpRequestInviteProcess)); //3308 RU
            //Recv.Add(unchecked((short) 0xA7A6), typeof (RpGuildAddMemberRequest)); //1606
            Recv.Add(unchecked((short) 0xEFE0), typeof (RpGuildGetMemberList)); //3308 RU
            //Recv.Add(unchecked((short) 0x6A2B), typeof (RpGuildInvite)); //1606
            Recv.Add(unchecked((short) 0xFF67), typeof (RpGuildGetHistory)); //3308 RU
            Recv.Add(unchecked((short) 0xDD03), typeof (RpGuildRemoveMember)); //3308 RU
            Recv.Add(unchecked((short) 0x7029), typeof (RpGuildChangeRankPrivileges)); //3308 RU
            Recv.Add(unchecked((short) 0x8A75), typeof (RpGuildRankAdd)); //3308 RU
            Recv.Add(unchecked((short) 0x901F), typeof (RpGuildChangeMemberRank)); //3308 RU
            Recv.Add(unchecked((short) 0xAEA2), typeof (RpGuildChangeLeader)); //3308 RU
            Recv.Add(unchecked((short) 0x968D), typeof (RpGuildMemberLeave)); //3308 RU
            Recv.Add(unchecked((short) 0x9DE5), typeof (RpServerGuildsPage)); //3308 RU
            Recv.Add(unchecked((short) 0xF494), typeof (RpGuildChangeAd)); //3308 RU
            Recv.Add(unchecked((short) 0x9D87), typeof (RpGuildChangeMotd)); //3308 RU
            Recv.Add(unchecked((short) 0xBFC1), typeof (RpGuildChangeTitle)); //3308 RU
            Recv.Add(unchecked((short) 0xD23D), typeof (RpGuildPraise)); //3308 RU
            Recv.Add(unchecked((short) 0xF22B), typeof (RpGuildMemberGuildPraise)); //3308 RU


            //Trade player vs player
            Recv.Add(unchecked((short) 0x6EDF), typeof (RpPlayerTradeAdd)); //3308 RU
            Recv.Add(unchecked((short) 0x6AAE), typeof (RpPlayerTradeRemoveItem)); //3308 RU
            Recv.Add(unchecked((short) 0x79BA), typeof (RpPlayerTradeLock)); //3308 RU
            Recv.Add(unchecked((short) 0xD457), typeof (RpTradeInterupt)); //3308 RU
            Recv.Add(unchecked((short) 0xE3C1), typeof (RpPlayerTradeCancel)); //3308 RU

            //Craft-Extract
            Recv.Add(unchecked((short) 0xC1E3), typeof (RpCraftStart)); //3308 RU
            Recv.Add(unchecked((short) 0x8B9C), typeof (RpExtractStart)); //3308 RU

            //Zone
            Recv.Add(unchecked((short) 0xAB32), typeof (RpZoneChange)); //3308 RU
            Recv.Add(unchecked((short) 0x4F4F), typeof (RpZoneUnk)); //3308 RU
            Recv.Add(unchecked((short) 0xD03F), typeof (RpZoneSwitchContinent)); //3308 RU

            //Warehouse
            Recv.Add(unchecked((short) 0xE0D4), typeof (RpWarehouseAddItem)); //3308 RU
            Recv.Add(unchecked((short) 0xD7B5), typeof (RpWarehouseAddItemToInventory)); //3308 RU
            Recv.Add(unchecked((short) 0xF085), typeof (RpWarehouseChangeSection)); //3308 RU
            Recv.Add(unchecked((short) 0xCCEC), typeof (RpWarehouseReplaceItem)); //3308 RU

            //mount
            Recv.Add(unchecked((short) 0x4F59), typeof (RpMountUnkQuestion)); //3308 RU

            //User interface
            Recv.Add(unchecked((short) 0x7D1B), typeof (RpUISettings));

            #endregion

            #region Server packets
            
            // Auth
            Send.Add(typeof (SpSendVersion), unchecked((short) 0x4DBD)); //all revs


            //Character
            Send.Add(typeof(SpSendCharacterList), unchecked((short)0xB38E)); //3308 RU
            Send.Add(typeof(SpCharacterCheckNameResult), unchecked((short)0xE315)); //3308 RU
            Send.Add(typeof(SpCharacterCreateResult), unchecked((short)0xC075)); //3308 RU
            Send.Add(typeof(SpCharacterDelete), unchecked((short)0xEFE4)); //3308 RU



            Send.Add(typeof(SpCharacterInit), unchecked((short)0xBD48)); //3308 RU

            Send.Add(typeof (SpCharacterStats), unchecked((short) 0xF0EB)); //3308 RU
            Send.Add(typeof (SpCharacterInfo), unchecked((short) 0xDE1C)); //3308 RU
            Send.Add(typeof (SpCharacterBind), unchecked((short) 0x9487)); //3308 RU
            Send.Add(typeof (SpCharacterPosition), unchecked((short) 0xD146)); //3308 RU
            Send.Add(typeof (SpCharacterThings), unchecked((short) 0xECF0)); //3308 RU
            Send.Add(typeof (SpCharacterEmotions), unchecked((short) 0xE0A0)); //3308 RU
            Send.Add(typeof (SpCharacterState), unchecked((short) 0xEBCD)); //3308 RU

            Send.Add(typeof (SpCharacterDeath), unchecked((short) 0x63E7)); //3308 RU
            Send.Add(typeof (SpCharacterMove), unchecked((short) 0xEBE2)); //3308 RU
            Send.Add(typeof (SpCharacterGatherstats), unchecked((short) 0x928F)); //3308 RU
            Send.Add(typeof (SpCharacterGuildInfo), unchecked((short) 0x581E)); //3308 RU
            Send.Add(typeof (SpCharacterRelation), unchecked((short) 0x866A)); //3308 RU
            Send.Add(typeof (SpRemoveCharacter), unchecked((short) 0x6256)); //3308 RU
            Send.Add(typeof (SpCharacterCraftStats), unchecked((short) 0xA046)); //3308 RU
            Send.Add(typeof (SpCharacterRecipes), unchecked((short) 0xCF01)); //3308 RU
            Send.Add(typeof (SpCharacterZoneData), unchecked((short) 0xE7C9)); //3308 RU

            //Chat
            Send.Add(typeof (SpChatMessage), unchecked((short) 0x5703)); //3308 RU
            Send.Add(typeof (SpChatPrivate), unchecked((short) 0xA082)); //3308 RU
            Send.Add(typeof (SpChatInfo), unchecked((short) 0xE321)); //3308 RU

            //Skills
            Send.Add(typeof (SpSkillList), unchecked((short) 0xC1D9)); //3308 RU
            Send.Add(typeof (SpAttack), unchecked((short) 0x69A2)); //3308 RU
            Send.Add(typeof (SpAttackDestination), unchecked((short) 0x56D8)); //3308 RU
            Send.Add(typeof (SpAttackShowBlock), unchecked((short) 0xD0C3)); //3308 RU
            Send.Add(typeof (SpAttackResult), unchecked((short) 0x9D8F)); //3308 RU
            Send.Add(typeof (SpAttackEnd), unchecked((short) 0xBF7C)); //3308 RU
            Send.Add(typeof (SpSkillCooldown), unchecked((short) 0xA848)); //3308 RU
            Send.Add(typeof (SpProjectile), unchecked((short) 0x7D90)); //3308 RU
            Send.Add(typeof (SpRemoveProjectile), unchecked((short) 0x7CD2)); //3308 RU
            Send.Add(typeof (SpItemCooldown), unchecked((short) 0xDDE0)); //3308 RU
            Send.Add(typeof (SpMarkTarget), unchecked((short) 0x8625)); //3308 RU

            //Npc
            Send.Add(typeof (SpNpcInfo), unchecked((short) 0x8AB2)); //3308 RU
            Send.Add(typeof (SpRemoveNpc), unchecked((short) 0x73B1)); //3308 RU
            Send.Add(typeof (SpNpcEmotion), unchecked((short) 0xC7DB)); //3308 RU
            Send.Add(typeof (SpNpcStatus), unchecked((short) 0x68B1)); //3308 RU
            Send.Add(typeof (SpNpcMove), unchecked((short) 0x580E)); //3308 RU
            Send.Add(typeof (SpNpcHpMp), unchecked((short) 0xFB12)); //3308 RU
            Send.Add(typeof (SpNpcHpWindow), unchecked((short) 0xC0B2)); //3308 RU
            Send.Add(typeof (SpNpcIcon), unchecked((short) 0xBF6B)); //3308 RU
            Send.Add(typeof (SpNpcTalk), unchecked((short) 0xD465)); //3308 RU

            //Exit actions
            Send.Add(typeof (SpRelogWindow), unchecked((short) 0xBFFE)); //3308 RU
            Send.Add(typeof (SpExitWindow), unchecked((short) 0xE6D6)); //3308 RU
            Send.Add(typeof (SpRelog), unchecked((short) 0x87C8)); //3308 RU
            Send.Add(typeof (SpExit), unchecked((short) 0xAE39)); //3308 RU

            Send.Add(typeof (SpShowDialog), unchecked((short) 0xD13D)); //3308 RU
            Send.Add(typeof (SpShowWindow), unchecked((short) 0xB797)); //3308 RU
            Send.Add(typeof (SpDropInfo), unchecked((short) 0xE764)); //3308 RU
            Send.Add(typeof (SpShowIcon), unchecked((short) 0x9C9C)); //3308 RU
            Send.Add(typeof (SpRemoveItem), unchecked((short) 0xDDBB)); //3308 RU
            Send.Add(typeof (SpInventory), unchecked((short) 0x92C0)); //3308 RU
            Send.Add(typeof (SpLevelUp), unchecked((short) 0x66A5)); //3308 RU
            Send.Add(typeof (SpUpdateExp), unchecked((short) 0x8FC7)); //3308 RU
            Send.Add(typeof (SpDeathDialog), unchecked((short) 0x5FCE)); //3308 RU
            //Send.Add(typeof (SpCharacterBuffs), unchecked((short) 0xCA86)); //1512
            Send.Add(typeof (SpAbnormal), unchecked((short) 0xC50E)); //3308 RU
            Send.Add(typeof (SpRemoveAbnormal), unchecked((short) 0xE3F4)); //3308 RU
            //Send.Add(typeof (SpHelp), unchecked((short) 0xB330)); //1512
            Send.Add(typeof (SpTradeList), unchecked((short) 0xC849)); //3308 RU
            Send.Add(typeof (SpUpdateTrade), unchecked((short) 0x733D)); //3308 RU
            Send.Add(typeof (SpDropPickup), unchecked((short) 0xAE81)); //3308 RU
            Send.Add(typeof (SpUpdateHp), unchecked((short) 0xD6BE)); //3308 RU
            Send.Add(typeof (SpUpdateMp), unchecked((short) 0xB5DF)); //3308 RU
            Send.Add(typeof (SpUpdateStamina), unchecked((short) 0x5CC4)); //3308 RU
            Send.Add(typeof (SpSystemMessage), unchecked((short) 0xF8D3)); //3308 RU
            Send.Add(typeof (SpDirectionChange), unchecked((short) 0x996F)); //3308 RU
            Send.Add(typeof (SpCampfire), unchecked((short) 0xB0BE)); //3308 RU
            Send.Add(typeof (SpRemoveCampfire), unchecked((short) 0xCB92)); //3308 RU
            Send.Add(typeof(SpRemoveHpBar), unchecked((short) 0xD7EE)); //3308 RU

            Send.Add(typeof (SpDuelCounter), unchecked((short) 0xC93C)); //3308 RU

            Send.Add(typeof (SpSkillPurchased), unchecked((short) 0x6235)); //3308 RU
            Send.Add(typeof (SpTraidSkillList), unchecked((short) 0xA774)); //3308 RU
            Send.Add(typeof (SpSystemWindow), unchecked((short) 0xF05B)); //3308 RU
            Send.Add(typeof (SpCanSendRequest), unchecked((short) 0x554E)); //3308 RU ???
            Send.Add(typeof (SpHideRequest), unchecked((short) 0xD627)); //3308 RU
            Send.Add(typeof (SpSystemNotice), unchecked((short) 0x5F11)); //3308 RU
            Send.Add(typeof (SpItemInfo), unchecked((short) 0x8E8C)); //3308 RU
            Send.Add(typeof (SpSimpleItemInfo), unchecked((short) 0xB359)); //3308 RU
            Send.Add(typeof (SpCreatureMoveTo), unchecked((short) 0xB983)); //3308 RU

            //Climb
            Send.Add(typeof (SpClimb), unchecked((short) 0xD9A1)); //3308 RU

            //Pegasus
            Send.Add(typeof (SpFlightPoints), unchecked((short) 0xDFDD)); //3308 RU
            Send.Add(typeof (SpPegasusInfo), unchecked((short) 0x953D)); //3308 RU
            Send.Add(typeof (SpPegasusFlight), unchecked((short) 0xC2E6)); //3308 RU
            Send.Add(typeof (SpPegasusFinishFly), unchecked((short) 0x98FD)); //3308 RU

            //Mounts
            Send.Add(typeof (SpMountShow), unchecked((short) 0x888D)); //3308 RU
            Send.Add(typeof (SpMountHide), unchecked((short) 0x888C)); //3308 RU
            Send.Add(typeof(SpMountUnkResponse), unchecked((short) 0xC82A)); //3308 RU

            // Inspect
            Send.Add(typeof(SpCharacterInspect), unchecked((short) 0xE153)); //3308 RU
            Send.Add(typeof(SpInspectUid), unchecked((short) 0x59A9)); //3308 RU
            Send.Add(typeof(SpInspectUnk), unchecked((short) 0x77C3)); //3308 RU

            //Quests
            Send.Add(typeof (SpQuest), unchecked((short) 0xE80F)); //3308 RU
            Send.Add(typeof (SpComplitedQuests), unchecked((short) 0xB791)); //3308 RU
            Send.Add(typeof (SpQuestComplite), unchecked((short) 0xA94D)); //3308 RU
            Send.Add(typeof (SpQuestMovie), unchecked((short) 0xFA43)); //3308 RU

            //Party
            Send.Add(typeof (SpPartyList), unchecked((short) 0xBD9C)); //3308 RU
            Send.Add(typeof(SpPartyRemoveMember), unchecked((short) 0xE230)); //3308 RU
            Send.Add(typeof (SpPartyStats), unchecked((short) 0xA908)); //3308 RU
            Send.Add(typeof(SpPartyLeave), unchecked((short) 0xC5A1)); //3308 RU
            Send.Add(typeof (SpPartyVote), unchecked((short) 0xF5B5)); //3308 RU
            Send.Add(typeof(SpPartyAbnormals), unchecked((short) 0xD7EB)); //3308 RU
            Send.Add(typeof(SpPartyMemberPosition), unchecked((short) 0xACC1)); //3308 RU

            //Guilds
            Send.Add(typeof(SpServerGuilds), unchecked((short) 0x6B0A)); //3308 RU
            Send.Add(typeof (SpRequestInvite), unchecked((short) 0x7EA4)); //3308 RU
            Send.Add(typeof (SpGuildMemberList), unchecked((short) 0x90E0)); //3308 RU
            Send.Add(typeof (SpGuildRanking), unchecked((short) 0x80BC)); //3308 RU
            Send.Add(typeof (SpGuildHistory), unchecked((short) 0xA04E)); //3308 RU

            //Friends
            Send.Add(typeof (SpFriendAdd), unchecked((short) 0x5B48)); //3308 RU
            Send.Add(typeof (SpFriendList), unchecked((short) 0x820A)); //3308 RU
            Send.Add(typeof (SpFriendUpdate), unchecked((short) 0xACAC)); //3308 RU

            //Gathering
            Send.Add(typeof (SpGatherInfo), unchecked((short) 0x7102)); //3308 RU
            Send.Add(typeof (SpRemoveGather), unchecked((short) 0xB372)); //3308 RU
            Send.Add(typeof (SpGatherStart), unchecked((short) 0x61EA)); //3308 RU
            Send.Add(typeof (SpGatherProgress), unchecked((short) 0xC666)); //3308 RU
            Send.Add(typeof (SpGatherEnd), unchecked((short) 0xA769)); //3308 RU
            Send.Add(typeof (SpUpdateGather), unchecked((short) 0x67AE)); //3308 RU

            //Trade
            Send.Add(typeof (SpTradeWindow), unchecked((short) 0xDD9A)); //3308 RU
            Send.Add(typeof (SpPlayerTradeHistory), unchecked((short) 0xDF5C)); //3308 RU
            Send.Add(typeof (SpTradeHideWindow), unchecked((short) 0x8895)); //3308 RU

            //Craft/Extract
            Send.Add(typeof (SpCraftUpdateWindow), unchecked((short) 0xE876)); //3308 RU
            Send.Add(typeof (SpExtractProgressbar), unchecked((short) 0xF1E2)); //3308 RU
            Send.Add(typeof (SpCraftWindow), unchecked((short) 0x53C7)); //3308 RU
            Send.Add(typeof (SpCraftInitBar), unchecked((short) 0x648C)); //3308 RU
            Send.Add(typeof (SpCraftProgress), unchecked((short) 0xB8FC)); //3308 RU

            //Warehouse
            Send.Add(typeof(SpWarehouseItems), unchecked((short) 0x6505)); //3308 RU

            //Zone
            Send.Add(typeof (SpZoneUnkAnswer), unchecked((short) 0x9F91)); //3308 RU
            Send.Add(typeof (SpZoneUnkAnswer2), unchecked((short) 0xB0A1)); //3308 RU

            //UI Settings
            Send.Add(typeof(SpUISettings), unchecked((short) 0xE265));

            //AF6B 16000400000000

            // party kick vote
            // party buffs 9E8A04001000010050004F3800001000200034ADE605A05C0800020000002000300008ACE605B0A1030001000000300040006CACE60570AB11000100000040000000D0ACE60578B6080001000000500000007CC400000000000001
            // medal *Party maker*       1055010008000800000007000000EAAA404F00000000
            // medal *Expedition relief* 10550100080008000000890000003C68404F00000000
            // message *party has disbanned* 15D5060040003100390033000000

            Send.Add(typeof (SpForFun), unchecked((short) 0x7777)); //...
            // ReSharper restore RedundantCast

            #endregion

            SendNames = Send.ToDictionary(p => p.Value, p => p.Key.Name);
        }
    }
}