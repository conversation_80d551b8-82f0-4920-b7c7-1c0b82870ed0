﻿namespace Data.Enums.Item
{
    // ReSharper disable InconsistentNaming
    public enum ItemCategory
    {
        combat,
        magical,
        custormaize,
        pkitem,
        medal,
        service,
        earring,
        necklace,
        ring,
        skillbook,
        token,
        order,
        etc,
        petAdult,
        petOrb,
        dyeItem,
        dyeRaw,
        dyeRecipe,
        dual,
        lance,
        twohand,
        axe,
        circle,
        bow,
        staff,
        rod,
        accessoryHair,
        weaponMaterial,
        armorMaterial,
        keyItem,
        generalMaterial,
        fiber,
        metal,
        alchemy,
        leather,
        alchemyMaterial,
        fireworkMaterial,
        weaponComponent,
        armorComponent,
        bodyMail,
        handMail,
        feetMail,
        bodyLeather,
        handLeather,
        feetLeather,
        bodyRobe,
        handRobe,
        feetRobe,
        recipe,
        quest,
        document,
        crestPoint,
        crest,
        charm,
        extractRecipe,
        accessoryFace,
        style_face,
        style_hair,
        style_dual,
        style_lance,
        style_twohand,
        style_axe,
        style_circle,
        style_bow,
        style_staff,
        style_rod
    }

    // ReSharper restore InconsistentNaming
}
