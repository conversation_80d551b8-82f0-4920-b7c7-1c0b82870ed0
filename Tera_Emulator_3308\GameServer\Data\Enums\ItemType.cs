﻿namespace Data.Enums
{
    public enum ItemType
    {
        Unknown,

        Axe,
        Bow,
        Disc,
        <PERSON>,
        <PERSON>,
        <PERSON>,
        Staff,
        TwoHand,

        HeavyBoots,
        HeavyChest,
        HeavyGlove,
        LightBoots,
        LightChest,
        LightGlove,
        RobeBoots,
        RobeChest,
        RobeGlove,

        Earring,
        Necklace,
        Ring,
        PromotionEarring,
        PromotionNecklace,
        PromotionRing,

        CancelEvadePotion,
        CancelLockonPotion,
        DecreaseAggroPotion,
        EaglesPotion,
        ExpBuffPotion,
        HpDurablePotion,
        HpPotion,
        LionsPotion,
        Potion,
        RarePotion,

        Bandage,
        PerfectBandage,

        Bomb,
        PetrolBomb,
        BoxBomb,

        CityRecall,
        DungeonRecall,
        TownRecall,

        Enchant,
        BlessEnchant,
        ShineEnchant,

        EventEgg,
        HomonEgg,

        Revival,
        RareRevival,

        Campfire,
        Crest,
        Customize,
        HomonPbrts,
        IdentifyScroll,
        MagicPowder,
        MysteryBox,
        PresentBox,
        Blackbottle,
        ElementalStone,
        Torch,
        RareMysterypaper,
        ScrollAtkSpeed,
        ScrollCriPower,
        ScrollCriRate,
        ScrollImpactPower,
        ShineMysteryPaper,
        TresureBox,
        UnidentifyScroll,

        GuildwarDeclare,
        GuildwarSurrender,
        SmallFirecracker,
        MediumFirecracker,
        SmileFirecracker,
        ArtisanLamp,
        Ricecake,
        WeaponScroll,
        Pumpkinpie,
        Candyapple,
        Eviloflight,
        HpDrug,
        MpDrug,
        HpDurableDrug,
        MpDurableDrug,
        ScrollMoveSpeed,
        ScrollDefProtect,
        ScrollDebuffProtect,
        ScrollSleepProtect,
        ScrollDotProtect,
        ScrollRunSpeed,
        ScrollHealPowerup,
        ExtractionLernScroll,
    }
}