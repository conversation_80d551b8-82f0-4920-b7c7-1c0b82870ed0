﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog
  xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <!-- make sure to set 'Copy To Output Directory' option for this file -->
  <!-- go to http://nlog-project.org/wiki/Configuration_file for more information -->

  <variable name="logDirectory" value="${basedir}/logs/${shortdate}"/>
  <variable name="ex" value="${onexception:${newline}EXCEPTION OCCURRED${newline}${exception:format=tostring}}"/>

  <targets>
    <target name="console" xsi:type="Console" layout="${time} | ${message}${ex}" />
    <target name="file" xsi:type="File" layout="${time} | ${message}${ex}" fileName="${logDirectory}/${level}.txt" />
  </targets>

  <rules>
    <logger name="*" minlevel="Debug" writeTo="console" />
    <logger name="*" minlevel="Warn" writeTo="file" />
  </rules>
</nlog>