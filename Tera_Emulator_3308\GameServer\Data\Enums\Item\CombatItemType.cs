﻿namespace Data.Enums.Item
{
    // ReSharper disable InconsistentNaming
    public enum CombatItemType
    {
        DISPOSAL,
        CUSTO<PERSON>,
        NO_COMBAT,
        CH<PERSON>GE_NAME,
        <PERSON>QUIP_ACCESSORY,
        <PERSON><PERSON><PERSON><PERSON>OK,
        <PERSON><PERSON><PERSON><PERSON>_COIN,
        CURSED_DREAM_PIECE,
        BLESSED_DREAM_PIECE,
        <PERSON><PERSON><PERSON><PERSON>FY_SCROLL,
        CURSE_REMOVE_SCROLL,
        <PERSON><PERSON><PERSON><PERSON><PERSON>_SCROLL,
        CHANGE_COLOR_ITEM,
        MIX_DISPOSAL,
        <PERSON><PERSON><PERSON>_RACE,
        CH<PERSON><PERSON>_GENDER,
        <PERSON><PERSON><PERSON>_LOOKS,
        <PERSON>QUIP_WEAPON,
        START_GUILD_WAR,
        GIVE_UP_GUILD_WAR,
        CH<PERSON><PERSON><PERSON>_SCROLL,
        <PERSON><PERSON><PERSON><PERSON>_RESET_SCROLL,
        RESET_SCROLL,
        IMMEDIATE,
        EQUIP_ARMOR_BODY,
        <PERSON>QUIP_ARMOR_ARM,
        EQUIP_ARMOR_LEG,
        DOCUMENT,
        CREST_RESET,
        CREST_POINT,
        CREST,
        R<PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>_STYLE_ACCESSORY,
        <PERSON>QUI<PERSON>_STYLE_WEAPON,
    }

    // ReSharper restore InconsistentNaming
}
