﻿namespace Data.Enums
{
    //@npc:DialogNpcString
    public enum DialogNpcString
    {
        Dialog = 1,
        Quest = 2,
        Shop = 3,
        Bank = 4,
        CreateGuild = 5,
        LearnSkills = 6,
        AvailableQuests = 7,
        ParcelPost = 8,
        ViewBrokerage = 9,

        RefineItems = 11,
        FlightPoints = 12,
        ApplicantRegistraction = 13,
        Policy = 14,
        Taxation = 15,
        OpenIncubator = 16,
        EquipPet = 17,
        Crafting = 18,
        Teleport = 19,
        ManageGuildHall = 20,
        ViewBattleGroupList = 21,
        Taxation2 = 22,
        Teleport2 = 23,
        ExchangeMedals = 24,
        GuildLeveling = 25,
        AcceptStalwartAura = 26,
        RestoreStamina = 27,
        ViewBrokerage2 = 28,
        ResetPoint = 29,
        ExpandInventory = 30,
        GuildEmblem = 31,
        MissionListComplete = 32,
        RemodelEquipment = 33,
        RestoreEquipmentAppearance = 34,
        GuildBank = 35,

        BuyCrystals = 100,

        GuildWarGoods = 120,

        ShopForPets = 130,

        RedeemCupraEnsiforms = 140,
        RedeemArgentEnsiforms = 141,

        ClaimSupportRewards = 151,
        ClaimProgressRewards = 152,
        ClaimDedicationRewards = 153,
        ClaimFrontlinesRewards = 154,

        BuyGeneralGoods = 160,

        BuyBattlegroundSupplies = 170,

        WeaponsmithMaterials = 180,
        ArmorsmithMaterials = 181,
        LeatherworkMaterials = 182,
        TailoringMaterials = 183,
        FocuscraftMaterials = 184,
        AlchemyMaterials = 185,

        WeaponsmithDesign = 190,
        ArmorsmithDesign = 191,
        LeatherworkPatterns = 192,
        TailoringDesign = 193,
        FocuscraftDesign = 194,
        AlchemyFormulas = 195,

        BuyWarriorGlyphs = 201,
        BuyLancerGlyphs = 202,
        BuySlayerGlyphs = 203,
        BuyBerserkerGlyphs = 204,
        BuySorcererGlyphs = 205,
        BuyArcherGlyphs = 206,
        BuyMysticGlyphs = 207,
        BuyPriestGlyphs = 208,

        BuyArmor = 210,
        BuyWeapons = 211,

        SupportTokenMeleeWeapons = 221,
        ProgressTokenMeleeWeapons = 222,
        DedicationTokenMeleeWeapons = 223,
        FrontlinesTokenMeleeWeapons = 224,

        SupportTokenFoci = 231,
        ProgressTokenFoci = 232,
        DedicationTokenFoci = 233,
        FrontlinesFoci = 234,

        DyeShop = 240,

        VanarchSpecialtyStore = 250,

        EquipmentTemplates = 260,

        RedeemOxbowBadges = 270,
        RedeemTrialBadges = 271,
        RedeemNecroticBadges = 272,
        RedeemDualityBadges = 273,
        RedeemEldritchBadges = 274,
        RedeemTrainingBadges = 275,

        RedeemLulunpangsGift = 280,
        RedeemYokisGift = 281,
        RedeemShanasGift = 282,
        RedeemNepirsGift = 283,

        AntiqueShop = 300,

        ExchangePracticumBadge = 310,
        ExchangeDireBadge = 311,
        ExchangeDireBadge2 = 312,
    }
}