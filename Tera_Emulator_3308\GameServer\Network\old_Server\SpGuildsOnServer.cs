﻿using System.IO;

namespace Network.Server
{
    public class SpGuildsOnServer : ASendPacket
    {
        public override void Write(BinaryWriter writer)
        {
            WriteB(writer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
        }
    }
}
